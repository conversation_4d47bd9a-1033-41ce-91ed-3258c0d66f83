import LegalDocumentScreen from '@/components/LegalDocument';
import strings from '@/constants/strings';
import React from 'react';
import { StyleSheet } from 'react-native';
const TermsAndServices: React.FC = () => {
  // TODO: use API for content
  return (
    <LegalDocumentScreen title={strings.termsAndServices.title} content={strings.termsAndServices.content}/>
  )
};

export default TermsAndServices;

const styles = StyleSheet.create({})