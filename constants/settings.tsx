import { SettingsItemProps } from '@/components/SettingsItem';
import { StyleMoodBoard } from '@/components/StyleMoodBoard';
import { router } from 'expo-router';

// Settings Page
export const ACCOUNT_SETTINGS: SettingsItemProps[] = [
  {
    title: 'Personal Information',
    onPress: () => {
      router.push('/personal-information');
    },
  },
  {
    title: 'Style Profile',
    onPress: () => {
      router.push('/style-profile');
    },
  },
  {
    title: 'Privacy',
    onPress: () => { },
  },
  {
    title: 'Invite your friends',
    onPress: () => { },
  },
];

// Settings Page
export const PREFERENCES: SettingsItemProps[] = [
  {
    title: 'Vegan',
    itemKey: 'vegan',
    default: { label: 'Off', value: false },
    options: [
      { label: 'On', value: true },
      { label: 'Off', value: false },
    ],
  },
  {
    title: 'Time and Date',
    itemKey: 'timeAndDate',
    options: [
      { label: '12hr; dd:mm:year', value: 'DD-MM-YYYY hh:mm A' },
      { label: '12hr; mm:dd:year', value: 'MM-DD-YYYY hh:mm A' },
      { label: '24hr; dd:mm:year', value: 'DD-MM-YYYY HH:mm' },
      { label: '24hr; mm:dd:year', value: 'MM-DD-YYYY HH:mm' },
    ],
  },
  {
    title: 'Temperature',
    itemKey: 'temperature',
    options: [
      { label: 'Celcius', value: 'celcius' },
      { label: 'Fahrenheit', value: 'fahrenheit' },
    ],
  },
  {
    title: 'Weight',
    itemKey: 'weight',
    options: [
      { label: 'Kilograms', value: 'kilograms' },
      { label: 'Pounds', value: 'pounds' },
    ],
  },
  {
    title: 'Style Recommendations',
    itemKey: 'styleRecommendations',
    options: [
      { label: 'Always', value: true },
      { label: 'Never', value: false },
    ],
  },
  {
    title: 'Notifications',
    itemKey: 'notifications',
    options: [
      { label: 'On', value: true },
      { label: 'Off', value: false },
    ],
  },
  {
    title: 'Location',
    itemKey: 'location',
    options: [
      { label: 'On', value: true },
      { label: 'Off', value: false },
    ],
  },
  {
    title: 'My Activity',
    onPress: () => { },
  },
];

// Settings Page
export const LEGAL_POLICIES: SettingsItemProps[] = [
  {
    title: 'Terms and services',
    onPress: () => {
      router.push('/terms-services');
    },
  },
  {
    title: 'Privacy Policy and Data Collection',
    onPress: () => {
      router.push('/privacy-policy');
    },
  },
];

// Style Profile Page
export const STYLE_MOOD_BOARD: SettingsItemProps[] = [
  {
    title: 'Style Mood Board ✨',
    onPress: () => { },
    children: <StyleMoodBoard />,
  },
];

// Style Profile Page
export const STYLE_PREFERENCES: SettingsItemProps[] = [
  {
    title: 'Style Keywords',
    itemKey: 'styleKeywords',
    onPress: () => {
      router.push('/style-keywords');
    },
  },
  {
    title: 'Sizing Preferences',
    itemKey: 'sizingPreferences',
    onPress: () => {
      router.push('/sizing-preferences');
    },
  },
  {
    title: 'Style Hardpasses',
    itemKey: 'styleHardpasses',
    onPress: () => {
      router.push('/style-hardpasses');
    },
  },
  {
    title: 'Style Color Match',
    itemKey: 'styleColorMatch',
    onPress: () => {
      router.push('/style-color-match');
    },
  },
];

// Sizing Preferences Page
export const SIZING__SETTING: SettingsItemProps[] = [
  {
    title: 'Sizing Standard',
    itemKey: 'sizingStandard',
    options: [
      { label: 'UK', value: 'uk' },
      { label: 'US', value: 'us' },
      { label: 'EU', value: 'eu' },
      { label: 'FR', value: 'fr' },
      { label: 'IT', value: 'it' },
    ],
  },
];

// Sizing Preferences Page
export const GENERAL_APPAREL: SettingsItemProps[] = [
  {
    title: 'T-Shirt',
    itemKey: 'tshirtSize',
    options: [
      { label: 'Small', value: 'small' },
      { label: 'Medium', value: 'medium' },
      { label: 'Large', value: 'large' },
    ],
  },
  {
    title: 'Shirt',
    itemKey: 'shirtSize',
    options: [
      { label: 'Small', value: 'small' },
      { label: 'Medium', value: 'medium' },
      { label: 'Large', value: 'large' },
    ],
  },
  {
    title: 'Sweater',
    itemKey: 'sweaterSize',
    options: [
      { label: 'Small', value: 'small' },
      { label: 'Medium', value: 'medium' },
      { label: 'Large', value: 'large' },
    ],
  },
  {
    title: 'Jacket/Coat',
    itemKey: 'jacketCoat',
    options: [{ label: 'Set', value: 'set' }],
  },
];

// Sizing Preferences Page
export const BOTTOMS: SettingsItemProps[] = [
  {
    title: 'Waist',
    itemKey: 'waist',
    options: [{ label: 'Set', value: 'set' }],
  },
  {
    title: 'Hip',
    itemKey: 'hip',
    options: [{ label: 'Set', value: 'set' }],
  },
  {
    title: 'Pants',
    itemKey: 'pants',
    options: [{ label: 'Set', value: 'set' }],
  },
  {
    title: 'Shorts',
    itemKey: 'shorts',
    options: [{ label: 'Set', value: 'set' }],
  },
];

// Sizing Preferences Page
export const FOOTWEAR: SettingsItemProps[] = [
  {
    title: 'Shoe',
    itemKey: 'shoe',
    options: [{ label: 'Set', value: 'set' }],
  },
  {
    title: 'Boots',
    itemKey: 'boots',
    options: [
      { label: 'Set1', value: 'set1' },
      { label: 'Set2', value: 'set2' },
      { label: 'Set3', value: 'set3' },
    ],
  },
];
