import React from 'react';
import HeaderPage from '@/components/common/HeaderPage';
import SectionTitle from '@/components/common/SectionTitle';
import { TextContent, TextFooter, TextItem, TextTitle } from './style';
import { Text, View } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';

interface LegalDocumentScreenProps {
  title: string;
  content: string | Array<{ title: string; description: string; items?: Array<{ title: string; description: string; }>; footer?: string }>;
}

const LegalDocumentScreen: React.FC<LegalDocumentScreenProps> = ({ title, content }) => {
  return (
    <View style={{ height: '100%' }}>
      <HeaderPage noLogo />
      <ScrollView>
      <View style={{ marginTop: 32 }}>
        <SectionTitle title={title} />
        {typeof content === 'string' ?
          <TextContent>{content}</TextContent>
        : content.map((item, index) => (
          <View key={index} style={{ marginTop: 24 }}>
            <TextTitle>{item.title}</TextTitle>
            {item.description && <TextItem>{item.description}</TextItem>}
            {item.items?.map((item, index) => (
              <Text key={index} style={{ marginTop: 8, marginLeft: 20 }}>
                <TextTitle>{item.title + ' '}</TextTitle>
                {item.description && <TextItem>{item.description}</TextItem>}
              </Text>
            ))}
            {item.footer && <TextFooter>{item.footer}</TextFooter>}
          </View>
        ))}
      </View>
      </ScrollView>
    </View>
  );
};

export default LegalDocumentScreen;
